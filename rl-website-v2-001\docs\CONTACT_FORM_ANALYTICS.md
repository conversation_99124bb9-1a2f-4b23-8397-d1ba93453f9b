# Contact Form Analytics Documentation

This document outlines the comprehensive analytics and SEO data collection implemented in the Ringerike Landskap contact form.

## Overview

The contact form has been enhanced with extensive client information tracking capabilities to provide valuable insights for marketing, SEO analysis, and user behavior understanding. When a user submits the contact form, a rich set of metadata is collected and sent along with the form submission.

## Data Categories

The collected data is organized into the following categories:

### 1. Basic Client Information

```
Device: Desktop
OS: Windows
Browser: Chrome
Language: en-US
Screen: 3840x2160
Country: Norge
Region: Viken
Local Time: 2025.05.07, Kl.19:07
Referrer: Direct
Visit Count: 98
Days Since First Visit: 3
```

### 2. Performance Metrics

```
Page Load Time: 5.37s
First Contentful Paint: 0.82s
Largest Contentful Paint: 1.24s
Time to Interactive: 1.15s
Cumulative Layout Shift: 0.002
```

### 3. User Behavior Data

```
Time on Site: 0m 6s
Session Duration: 0m 0s
Pages Viewed: 3
Bounce: No
Max Scroll Depth: 0%
Entry Page: /kontakt
Current Page: /kontakt (Kontakt oss | Ringerike Landskap | Ringerike Landskap)
Last Visit: 2025.05.07, Kl.19:07
Navigation Path: /kontakt
```

### 4. Engagement Metrics

```
Total Interactions: 12
CTA Clicks: 2
Form Interactions: 5
Time to First Interaction: 3.45s
```

### 5. Conversion Data

```
Conversion Path: /home → /tjenester → /kontakt
Time to Conversion: 2m 15s
Returning Visitor Conversion: Yes
Previous Sites: google.com, finn.no
```

### 6. Marketing Data

```
Search Keywords: anleggsgartner ringerike
Source: google
Medium: organic
Campaign: spring2025
Term: landscaping
Content: homepage_banner
```

## Implementation Details

### Data Collection Methods

The contact form uses a combination of browser APIs and local storage to collect and track user data:

1. **Device Information**: Collected using `navigator.userAgent` and other browser APIs
2. **Performance Metrics**: Collected using the Performance API (`window.performance`)
3. **User Behavior**: Tracked using session storage and event listeners
4. **Engagement Metrics**: Tracked using event listeners for clicks, scrolls, and form interactions
5. **Conversion Data**: Built from session history stored in localStorage
6. **Marketing Data**: Extracted from URL parameters and referrer information

### Storage Mechanisms

- **localStorage**: Used for persistent data across sessions (visit count, first visit date)
- **sessionStorage**: Used for session-specific data (entry page, navigation path)
- **React state**: Used to maintain the current state of all collected data

### Privacy Considerations

- All data is collected client-side and only sent when the user explicitly submits the form
- No personally identifiable information is collected beyond what the user provides in the form
- Data collection complies with GDPR requirements as it's necessary for the legitimate interest of improving the website

## Technical Implementation

The implementation is located in `src/sections/60-contact/index.tsx` and includes:

1. A comprehensive `ClientInfo` interface that defines all collected data points
2. Event listeners for tracking user interactions
3. Functions for collecting and formatting the data
4. Integration with the form submission process

### Key Code Sections

#### ClientInfo Interface

The `ClientInfo` interface defines all the data points collected:

```typescript
interface ClientInfo {
  // Basic device info
  device: string;
  os: string;
  browser: string;
  browserLanguage: string;
  screenSize: string;
  
  // Time metrics
  timeOnSite: number;
  sessionDuration: number;
  
  // Referral info
  referrer: string;
  entryPage: string;
  currentPage: string;
  currentPageTitle: string;
  
  // Marketing parameters
  utmSource: string;
  utmMedium: string;
  utmCampaign: string;
  utmTerm: string;
  utmContent: string;
  searchKeywords: string;
  
  // Visit metrics
  visitCount: number;
  pagesViewedCount: number;
  isBounce: boolean;
  daysFromFirstVisit: number;
  
  // Performance metrics
  pageLoadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  timeToInteractive: number;
  cumulativeLayoutShift: number;
  
  // Location info
  country: string;
  region: string;
  localTime: string;
  
  // User journey
  lastVisit: string;
  navigationPath: string[];
  maxScrollDepth: number;
  
  // Engagement metrics
  interactionsCount: number;
  ctaClicks: number;
  formInteractions: number;
  timeToFirstInteraction: number;
  
  // Conversion data
  conversionPath: string[];
  timeToConversion: number;
  previousSites: string[];
  returningVisitorConversion: boolean;
}
```

## SEO Benefits

The enhanced contact form provides several SEO benefits:

1. **User Intent Understanding**: Better understanding of how users find and interact with the site
2. **Content Optimization**: Insights into which pages lead to conversions
3. **Technical Performance**: Data on how site performance affects conversion rates
4. **Local SEO**: Improved understanding of geographic user distribution
5. **Keyword Insights**: Direct data on which search terms drive conversions

## Future Enhancements

Potential future enhancements to the analytics system:

1. Integration with Google Analytics for consolidated reporting
2. A/B testing capabilities to compare form variations
3. Heatmap integration for more detailed interaction tracking
4. Expanded performance metrics using the Web Vitals API
5. Enhanced geographic targeting based on collected location data

## Conclusion

The enhanced contact form analytics provide a comprehensive view of user behavior, technical performance, and marketing effectiveness. This data is invaluable for optimizing the website, improving user experience, and refining marketing strategies.
