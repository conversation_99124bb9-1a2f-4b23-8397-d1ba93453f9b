import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Download, Image, Palette, Settings, ChevronDown } from 'lucide-react';

interface LogoConfig {
  size: string;
  type: string;
  format: string;
  colorspace: string;
  style: string;
  variant: string;
}

const LogoExporter = () => {
  const [config, setConfig] = useState<LogoConfig>({
    size: '2K',
    type: 'Digital',
    format: 'png',
    colorspace: 'RGB',
    style: 'Light',
    variant: 'a'
  });

  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [filename, setFilename] = useState<string>('');
  const [showAdvanced, setShowAdvanced] = useState<boolean>(false);

  // Options for each parameter
  const sizes = ['1K', '2K', '4K', '8K'];
  const types = ['Digital', 'Print'];
  const formats = {
    Digital: ['png', 'webp', 'jpg', 'svg'],
    Print: ['eps', 'pdf', 'svg', 'png']
  };
  const colorspaces = {
    Digital: ['RGB', 'sRGB'],
    Print: ['CMYK', 'RGB']
  };
  const styles = ['Light', 'Dark'];
  const variants = ['a', 'b', 'c'];

  // Map variants to layout names
  const variantMapping = {
    'a': 'layout1',
    'b': 'layout2', 
    'c': 'layout3'
  };

  // Update format and colorspace based on type
  useEffect(() => {
    const defaultFormats = {
      Digital: 'png',
      Print: 'eps'
    };
    const defaultColorspaces = {
      Digital: 'RGB',
      Print: 'CMYK'
    };

    setConfig(prev => ({
      ...prev,
      format: defaultFormats[config.type as keyof typeof defaultFormats],
      colorspace: defaultColorspaces[config.type as keyof typeof defaultColorspaces]
    }));
  }, [config.type]);

  // Generate filename and preview URL
  useEffect(() => {
    const layoutName = variantMapping[config.variant as keyof typeof variantMapping];
    const styleName = config.style.toLowerCase();
    const name = `RingerikeLandskap_logo_${config.variant}_${styleName}_${config.size}_${config.colorspace}_${config.type.toLowerCase()}.${config.format}`;
    setFilename(name);
    
    // Set preview URL to actual logo file - ensure correct path
    const previewPath = `/logos/${layoutName}/${styleName === 'light' ? 'bright' : 'dark'}.svg`;
    setPreviewUrl(previewPath);
    console.log('Preview URL set to:', previewPath);
  }, [config]);

  const handleConfigChange = (key: keyof LogoConfig, value: string) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleDownload = () => {
    // Get the current logo path
    const layoutName = variantMapping[config.variant as keyof typeof variantMapping];
    const styleName = config.style.toLowerCase() === 'light' ? 'bright' : 'dark';
    const logoPath = `/logos/${layoutName}/${styleName}.svg`;
    
    // Create download link
    const link = document.createElement('a');
    link.href = logoPath;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    console.log('Downloaded logo:', filename);
  };

  const getResolutionInfo = (size: string) => {
    const resolutions = {
      '1K': '1024px',
      '2K': '2048px', 
      '4K': '4096px',
      '8K': '8192px'
    };
    return resolutions[size as keyof typeof resolutions] || size;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-md mx-auto bg-white min-h-screen">

        {/* Mobile App Header */}
        <div className="bg-white border-b border-gray-200 px-4 py-3 sticky top-0 z-10">
          <div className="text-center">
            <h1 className="text-lg font-semibold text-gray-900">Logo Exporter</h1>
            <p className="text-xs text-gray-500 mt-1">Ringerike Landskap</p>
          </div>
        </div>

        {/* Mobile App Content */}
        <div className="px-4 pb-20">

          {/* 1. Configuration Section */}
          <div className="py-4">
            <h2 className="text-base font-medium text-gray-900 mb-4 flex items-center gap-2">
              <Settings className="h-4 w-4 text-gray-600" />
              Configuration
            </h2>

            {/* Style & Layout - Primary options */}
            <div className="space-y-4 mb-6">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">Style</Label>
                  <Select value={config.style} onValueChange={(value) => handleConfigChange('style', value)}>
                    <SelectTrigger className="h-11 text-sm border-gray-300">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {styles.map(style => (
                        <SelectItem key={style} value={style}>
                          {style}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">Layout</Label>
                  <Select value={config.variant} onValueChange={(value) => handleConfigChange('variant', value)}>
                    <SelectTrigger className="h-11 text-sm border-gray-300">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {variants.map(variant => (
                        <SelectItem key={variant} value={variant}>
                          Layout {variant.toUpperCase()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Technical Settings - Collapsible */}
            <div className="border border-gray-200 rounded-lg">
              <button
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="w-full px-4 py-3 flex items-center justify-between text-sm font-medium text-gray-700 bg-gray-50 rounded-lg"
              >
                <span>Advanced Settings</span>
                <ChevronDown className={`h-4 w-4 transition-transform ${showAdvanced ? 'rotate-180' : ''}`} />
              </button>

              {showAdvanced && (
                <div className="p-4 space-y-4 border-t border-gray-200">
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label className="text-sm font-medium text-gray-700 mb-2 block">Size</Label>
                      <Select value={config.size} onValueChange={(value) => handleConfigChange('size', value)}>
                        <SelectTrigger className="h-11 text-sm border-gray-300">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {sizes.map(size => (
                            <SelectItem key={size} value={size}>
                              {size}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-gray-700 mb-2 block">Type</Label>
                      <Select value={config.type} onValueChange={(value) => handleConfigChange('type', value)}>
                        <SelectTrigger className="h-11 text-sm border-gray-300">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {types.map(type => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label className="text-sm font-medium text-gray-700 mb-2 block">Format</Label>
                      <Select value={config.format} onValueChange={(value) => handleConfigChange('format', value)}>
                        <SelectTrigger className="h-11 text-sm border-gray-300">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {formats[config.type as keyof typeof formats].map(format => (
                            <SelectItem key={format} value={format}>
                              .{format.toUpperCase()}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm font-medium text-gray-700 mb-2 block">Color Space</Label>
                      <Select value={config.colorspace} onValueChange={(value) => handleConfigChange('colorspace', value)}>
                        <SelectTrigger className="h-11 text-sm border-gray-300">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {colorspaces[config.type as keyof typeof colorspaces].map(colorspace => (
                            <SelectItem key={colorspace} value={colorspace}>
                              {colorspace}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 2. Preview Section */}
          <div className="py-4">
            <h2 className="text-base font-medium text-gray-900 mb-4 flex items-center gap-2">
              <Image className="h-4 w-4 text-gray-600" />
              Logo Preview
            </h2>

            <div className={`${
              config.style === 'Dark'
                ? 'bg-gradient-to-br from-slate-800 to-slate-900'
                : 'bg-gradient-to-br from-white to-gray-50'
            } rounded-xl border border-gray-200 p-6 flex items-center justify-center h-64`}>
              {previewUrl ? (
                <img
                  src={previewUrl}
                  alt={`Ringerike Landskap Logo - ${config.style} Style, Variant ${config.variant.toUpperCase()}`}
                  className="w-full h-full object-contain"
                  onLoad={() => console.log('Logo loaded successfully:', previewUrl)}
                  onError={(e) => {
                    console.error('Logo preview failed to load:', previewUrl);
                    console.error('Error details:', e);
                  }}
                />
              ) : (
                <div className="text-center space-y-3">
                  <div className="w-12 h-12 bg-gray-200 rounded-lg mx-auto flex items-center justify-center">
                    <Palette className="h-6 w-6 text-gray-400" />
                  </div>
                  <p className="font-medium text-sm text-gray-500">Loading Preview...</p>
                </div>
              )}
            </div>
          </div>

          {/* 3. Download Section */}
          <div className="py-4">
            <h2 className="text-base font-medium text-gray-900 mb-4 flex items-center gap-2">
              <Download className="h-4 w-4 text-gray-600" />
              Download
            </h2>

            {/* File Info Card */}
            <div className="bg-gray-50 rounded-xl p-4 mb-4">
              <div className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-700 block mb-1">Generated Filename:</Label>
                  <div className="bg-white p-3 rounded-lg border border-gray-200">
                    <code className="text-xs text-gray-800 break-all">{filename}</code>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600 block">Resolution</span>
                    <span className="text-gray-900">{getResolutionInfo(config.size)}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 block">Format</span>
                    <span className="text-gray-900">{config.format.toUpperCase()}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 block">Color Space</span>
                    <span className="text-gray-900">{config.colorspace}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 block">Usage</span>
                    <span className="text-gray-900">{config.type}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Download Button */}
            <Button
              onClick={handleDownload}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white h-12 text-base font-medium rounded-xl"
            >
              <Download className="h-5 w-5 mr-2" />
              Download Logo
            </Button>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center py-6 px-4 text-gray-400 text-xs border-t border-gray-100">
          <p>© Ringerike Landskap - Professional Logo Assets</p>
        </div>

      </div>
    </div>
  );
};

export default LogoExporter;
