import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Download, Image, Palette, Settings } from 'lucide-react';

interface LogoConfig {
  size: string;
  type: string;
  format: string;
  colorspace: string;
  style: string;
  variant: string;
}

const LogoExporter = () => {
  const [config, setConfig] = useState<LogoConfig>({
    size: '2K',
    type: 'Digital',
    format: 'png',
    colorspace: 'RGB',
    style: 'Light',
    variant: 'a'
  });

  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [filename, setFilename] = useState<string>('');

  // Options for each parameter
  const sizes = ['1K', '2K', '4K', '8K'];
  const types = ['Digital', 'Print'];
  const formats = {
    Digital: ['png', 'webp', 'jpg', 'svg'],
    Print: ['eps', 'pdf', 'svg', 'png']
  };
  const colorspaces = {
    Digital: ['RGB', 'sRGB'],
    Print: ['CMYK', 'RGB']
  };
  const styles = ['Light', 'Dark'];
  const variants = ['a', 'b', 'c'];

  // Map variants to layout names
  const variantMapping = {
    'a': 'layout1',
    'b': 'layout2', 
    'c': 'layout3'
  };

  // Update format and colorspace based on type
  useEffect(() => {
    const defaultFormats = {
      Digital: 'png',
      Print: 'eps'
    };
    const defaultColorspaces = {
      Digital: 'RGB',
      Print: 'CMYK'
    };

    setConfig(prev => ({
      ...prev,
      format: defaultFormats[config.type as keyof typeof defaultFormats],
      colorspace: defaultColorspaces[config.type as keyof typeof defaultColorspaces]
    }));
  }, [config.type]);

  // Generate filename and preview URL
  useEffect(() => {
    const layoutName = variantMapping[config.variant as keyof typeof variantMapping];
    const styleName = config.style.toLowerCase();
    const name = `RingerikeLandskap_logo_${config.variant}_${styleName}_${config.size}_${config.colorspace}_${config.type.toLowerCase()}.${config.format}`;
    setFilename(name);
    
    // Set preview URL to actual logo file - ensure correct path
    const previewPath = `/logos/${layoutName}/${styleName === 'light' ? 'bright' : 'dark'}.svg`;
    setPreviewUrl(previewPath);
    console.log('Preview URL set to:', previewPath);
  }, [config]);

  const handleConfigChange = (key: keyof LogoConfig, value: string) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleDownload = () => {
    // Get the current logo path
    const layoutName = variantMapping[config.variant as keyof typeof variantMapping];
    const styleName = config.style.toLowerCase() === 'light' ? 'bright' : 'dark';
    const logoPath = `/logos/${layoutName}/${styleName}.svg`;
    
    // Create download link
    const link = document.createElement('a');
    link.href = logoPath;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    console.log('Downloaded logo:', filename);
  };

  const getResolutionInfo = (size: string) => {
    const resolutions = {
      '1K': '1024px',
      '2K': '2048px', 
      '4K': '4096px',
      '8K': '8192px'
    };
    return resolutions[size as keyof typeof resolutions] || size;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="max-w-6xl mx-auto">

        {/* Header - Compact on mobile */}
        <div className="text-center py-4 md:py-8 px-4">
          <h1 className="text-2xl md:text-4xl font-bold text-slate-800 mb-1 md:mb-2">Logo Exporter</h1>
          <p className="text-slate-600 text-sm md:text-lg">Ringerike Landskap - Professional Logo Downloads</p>
          <div className="mt-2 md:mt-4 flex justify-center">
            <Badge variant="outline" className="text-xs md:text-sm">
              ringerikelandskap.no/logoexporter
            </Badge>
          </div>
        </div>

        {/* Logical workflow: Configuration → Preview → Download */}
        <div className="flex flex-col lg:grid lg:grid-cols-2 gap-4 md:gap-6 px-4 pb-4">

          {/* 1. Configuration Panel - Always first */}
          <Card className="shadow-lg border-0">
            <CardHeader className="bg-slate-800 text-white rounded-t-lg py-3 md:py-4">
              <CardTitle className="flex items-center gap-2 text-sm md:text-base">
                <Settings className="h-4 w-4 md:h-5 md:w-5" />
                Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3 md:p-6 space-y-4 md:space-y-6">

              {/* Most important options first on mobile */}
              {/* Style and Variant - Most visible impact */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
                <div className="space-y-2">
                  <Label className="text-xs md:text-sm font-medium text-slate-700">Style</Label>
                  <Select value={config.style} onValueChange={(value) => handleConfigChange('style', value)}>
                    <SelectTrigger className="w-full h-9 md:h-10">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {styles.map(style => (
                        <SelectItem key={style} value={style}>
                          {style}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-xs md:text-sm font-medium text-slate-700">Layout Variant</Label>
                  <Select value={config.variant} onValueChange={(value) => handleConfigChange('variant', value)}>
                    <SelectTrigger className="w-full h-9 md:h-10">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {variants.map(variant => (
                        <SelectItem key={variant} value={variant}>
                          Layout {variant.toUpperCase()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Technical specs in compact grid */}
              <div className="grid grid-cols-2 gap-3 md:gap-4">
                <div className="space-y-2">
                  <Label className="text-xs md:text-sm font-medium text-slate-700">Size</Label>
                  <Select value={config.size} onValueChange={(value) => handleConfigChange('size', value)}>
                    <SelectTrigger className="w-full h-9 md:h-10">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {sizes.map(size => (
                        <SelectItem key={size} value={size}>
                          {size}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-xs md:text-sm font-medium text-slate-700">Type</Label>
                  <Select value={config.type} onValueChange={(value) => handleConfigChange('type', value)}>
                    <SelectTrigger className="w-full h-9 md:h-10">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {types.map(type => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-xs md:text-sm font-medium text-slate-700">Format</Label>
                  <Select value={config.format} onValueChange={(value) => handleConfigChange('format', value)}>
                    <SelectTrigger className="w-full h-9 md:h-10">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {formats[config.type as keyof typeof formats].map(format => (
                        <SelectItem key={format} value={format}>
                          .{format.toUpperCase()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-xs md:text-sm font-medium text-slate-700">Color Space</Label>
                  <Select value={config.colorspace} onValueChange={(value) => handleConfigChange('colorspace', value)}>
                    <SelectTrigger className="w-full h-9 md:h-10">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {colorspaces[config.type as keyof typeof colorspaces].map(colorspace => (
                        <SelectItem key={colorspace} value={colorspace}>
                          {colorspace}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

            </CardContent>
          </Card>

          {/* 2. Preview Panel - Second */}
          <Card className="shadow-lg border-0">
            <CardHeader className="bg-slate-800 text-white rounded-t-lg py-3 md:py-4">
              <CardTitle className="flex items-center gap-2 text-sm md:text-base">
                <Image className="h-4 w-4 md:h-5 md:w-5" />
                Logo Preview
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3 md:p-6">
              <div className={`${
                config.style === 'Dark'
                  ? 'bg-gradient-to-br from-slate-800 to-slate-900'
                  : 'bg-gradient-to-br from-white to-gray-50'
              } rounded-lg p-2 md:p-4 flex items-center justify-center h-[200px] md:h-[300px] lg:h-[400px]`}>
                {previewUrl ? (
                  <img
                    src={previewUrl}
                    alt={`Ringerike Landskap Logo - ${config.style} Style, Variant ${config.variant.toUpperCase()}`}
                    className="w-full h-full object-contain"
                    onLoad={() => console.log('Logo loaded successfully:', previewUrl)}
                    onError={(e) => {
                      console.error('Logo preview failed to load:', previewUrl);
                      console.error('Error details:', e);
                    }}
                  />
                ) : (
                  <div className="text-center space-y-2">
                    <div className="w-12 h-12 md:w-16 md:h-16 bg-slate-700 rounded-lg mx-auto flex items-center justify-center">
                      <Palette className="h-6 w-6 md:h-8 md:w-8 text-white" />
                    </div>
                    <p className={`font-medium text-sm md:text-base ${
                      config.style === 'Dark' ? 'text-slate-300' : 'text-slate-600'
                    }`}>Loading Preview...</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

        </div>

        {/* 3. Download Section - Third, full width */}
        <div className="px-4 pb-4">
          <Card className="shadow-lg border-0">
            <CardHeader className="bg-green-600 text-white rounded-t-lg py-3 md:py-4">
              <CardTitle className="flex items-center gap-2 text-sm md:text-base">
                <Download className="h-4 w-4 md:h-5 md:w-5" />
                Download
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3 md:p-6 space-y-3 md:space-y-4">
              <div className="space-y-2">
                <Label className="text-xs md:text-sm font-medium text-slate-700">Generated Filename:</Label>
                <div className="bg-slate-100 p-2 md:p-3 rounded-md">
                  <code className="text-xs md:text-sm text-slate-800 break-all">{filename}</code>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 md:gap-4 text-xs md:text-sm">
                <div>
                  <span className="font-medium text-slate-600">Resolution:</span>
                  <p>{getResolutionInfo(config.size)}</p>
                </div>
                <div>
                  <span className="font-medium text-slate-600">Format:</span>
                  <p>{config.format.toUpperCase()}</p>
                </div>
                <div>
                  <span className="font-medium text-slate-600">Color Space:</span>
                  <p>{config.colorspace}</p>
                </div>
                <div>
                  <span className="font-medium text-slate-600">Usage:</span>
                  <p>{config.type}</p>
                </div>
              </div>

              <Button
                onClick={handleDownload}
                className="w-full bg-green-600 hover:bg-green-700 text-white text-sm md:text-base"
                size="lg"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Logo
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Footer - Compact on mobile */}
        <div className="text-center py-3 md:py-4 px-4 text-slate-500 text-xs md:text-sm">
          <p>© Ringerike Landskap - Professional Logo Assets</p>
        </div>

      </div>
    </div>
  );
};

export default LogoExporter;
