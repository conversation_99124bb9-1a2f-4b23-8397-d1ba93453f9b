import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Download, Image, Palette, Settings, ChevronDown } from 'lucide-react';

interface LogoConfig {
  size: string;
  format: string;
  style: string;
  variant: string;
}

const LogoExporter = () => {
  const [config, setConfig] = useState<LogoConfig>({
    size: '2K',
    format: 'svg',
    style: 'Light',
    variant: 'a'
  });

  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [filename, setFilename] = useState<string>('');
  const [showAdvanced, setShowAdvanced] = useState<boolean>(false);

  // Options for each parameter
  const sizes = ['1K', '2K', '4K', '8K'];
  const formats = ['svg', 'png', 'jpg'];
  const styles = ['Light', 'Dark'];
  const variants = ['a', 'b', 'c'];

  // Map variants to layout names
  const variantMapping = {
    'a': 'layout1',
    'b': 'layout2', 
    'c': 'layout3'
  };



  // Generate filename and preview URL
  useEffect(() => {
    const layoutName = variantMapping[config.variant as keyof typeof variantMapping];
    const styleName = config.style.toLowerCase();
    const name = `RingerikeLandskap_logo_${config.variant}_${styleName}_${config.size}.${config.format}`;
    setFilename(name);

    // Set preview URL to actual logo file - ensure correct path
    const previewPath = `/logos/${layoutName}/${styleName === 'light' ? 'bright' : 'dark'}.svg`;
    setPreviewUrl(previewPath);
    console.log('Preview URL set to:', previewPath);
  }, [config]);

  const handleConfigChange = (key: keyof LogoConfig, value: string) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleDownload = async () => {
    try {
      console.log('Starting download process...');
      const layoutName = variantMapping[config.variant as keyof typeof variantMapping];
      const styleName = config.style.toLowerCase() === 'light' ? 'bright' : 'dark';
      const logoPath = `/logos/${layoutName}/${styleName}.svg`;

      console.log('Logo path:', logoPath);
      console.log('Format:', config.format);
      console.log('Filename:', filename);

      if (config.format === 'svg') {
        // Direct SVG download
        console.log('Direct SVG download');
        const response = await fetch(logoPath);
        if (!response.ok) {
          throw new Error(`Failed to fetch SVG: ${response.status}`);
        }

        const svgBlob = await response.blob();
        const link = document.createElement('a');
        link.href = URL.createObjectURL(svgBlob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      } else {
        // Convert SVG to PNG/JPG
        console.log('Converting SVG to', config.format);
        await convertAndDownloadSVG(logoPath, config.format, filename);
      }

      console.log('Download completed successfully:', filename);
    } catch (error) {
      console.error('Download failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      alert(`Download failed: ${errorMessage}\n\nPlease try again or contact support.`);
    }
  };

  const convertAndDownloadSVG = async (svgPath: string, format: string, filename: string) => {
    try {
      // Fetch SVG content
      const response = await fetch(svgPath);
      if (!response.ok) {
        throw new Error(`Failed to fetch SVG: ${response.status}`);
      }

      const svgText = await response.text();
      console.log('SVG fetched successfully, length:', svgText.length);

      // Parse SVG to get dimensions
      const parser = new DOMParser();
      const svgDoc = parser.parseFromString(svgText, 'image/svg+xml');
      const svgElement = svgDoc.querySelector('svg');

      if (!svgElement) {
        throw new Error('Invalid SVG file - no SVG element found');
      }

      // Get size multiplier based on config
      const sizeMultiplier = {
        '1K': 1024,
        '2K': 2048,
        '4K': 4096,
        '8K': 8192
      }[config.size] || 2048;

      // Get SVG viewBox or default dimensions
      const viewBox = svgElement.getAttribute('viewBox');
      let width = sizeMultiplier;
      let height = sizeMultiplier;

      if (viewBox) {
        const [, , vbWidth, vbHeight] = viewBox.split(' ').map(Number);
        if (vbWidth && vbHeight) {
          const aspectRatio = vbWidth / vbHeight;
          if (aspectRatio > 1) {
            height = Math.round(width / aspectRatio);
          } else {
            width = Math.round(height * aspectRatio);
          }
        }
      }

      console.log('Canvas dimensions:', width, 'x', height);

      // Create canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('Canvas not supported');
      }

      canvas.width = width;
      canvas.height = height;

      // Set background for JPG format
      if (format === 'jpg') {
        const backgroundColor = config.style === 'Dark'
          ? '#1e293b' // slate-800 equivalent
          : '#ffffff'; // white

        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, width, height);
        console.log('Background set for JPG:', backgroundColor);
      }

      // Modify SVG to ensure it has proper dimensions
      const modifiedSvg = svgText.replace(
        /<svg([^>]*)>/,
        `<svg$1 width="${width}" height="${height}">`
      );

      // Create image from SVG
      const img = new Image();
      img.crossOrigin = 'anonymous';

      const svgBlob = new Blob([modifiedSvg], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);

      return new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          URL.revokeObjectURL(url);
          reject(new Error('Image loading timeout'));
        }, 10000); // 10 second timeout

        img.onload = () => {
          clearTimeout(timeout);
          console.log('Image loaded successfully');

          try {
            // Draw image on canvas
            ctx.drawImage(img, 0, 0, width, height);
            console.log('Image drawn on canvas');

            // Convert to desired format
            const mimeType = format === 'png' ? 'image/png' : 'image/jpeg';
            const quality = format === 'jpg' ? 0.95 : undefined;

            canvas.toBlob((blob) => {
              if (!blob) {
                reject(new Error('Failed to create image blob'));
                return;
              }

              console.log('Blob created, size:', blob.size);

              // Download the converted image
              const link = document.createElement('a');
              link.href = URL.createObjectURL(blob);
              link.download = filename;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);

              // Cleanup
              URL.revokeObjectURL(url);
              URL.revokeObjectURL(link.href);
              console.log('Download initiated');
              resolve();
            }, mimeType, quality);
          } catch (error) {
            URL.revokeObjectURL(url);
            reject(error);
          }
        };

        img.onerror = (error) => {
          clearTimeout(timeout);
          URL.revokeObjectURL(url);
          console.error('Image loading error:', error);
          reject(new Error('Failed to load SVG image'));
        };

        console.log('Setting image source...');
        img.src = url;
      });
    } catch (error) {
      console.error('SVG conversion error:', error);
      throw error;
    }
  };

  const getResolutionInfo = (size: string) => {
    const resolutions = {
      '1K': '1024px',
      '2K': '2048px', 
      '4K': '4096px',
      '8K': '8192px'
    };
    return resolutions[size as keyof typeof resolutions] || size;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-md mx-auto bg-white min-h-screen">

        {/* Mobile App Header */}
        <div className="bg-white border-b border-gray-200 px-4 py-3 sticky top-0 z-10">
          <div className="text-center">
            <h1 className="text-lg font-semibold text-gray-900">Logo Exporter</h1>
            <p className="text-xs text-gray-500 mt-1">Ringerike Landskap</p>
          </div>
        </div>

        {/* Mobile App Content */}
        <div className="px-4 pb-20">

          {/* 1. Configuration Section */}
          <div className="py-4">
            <h2 className="text-base font-medium text-gray-900 mb-4 flex items-center gap-2">
              <Settings className="h-4 w-4 text-gray-600" />
              Configuration
            </h2>

            {/* Style & Layout - Primary options */}
            <div className="space-y-4 mb-6">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">Style</Label>
                  <Select value={config.style} onValueChange={(value) => handleConfigChange('style', value)}>
                    <SelectTrigger className="h-11 text-sm border-gray-300">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {styles.map(style => (
                        <SelectItem key={style} value={style}>
                          {style}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">Layout</Label>
                  <Select value={config.variant} onValueChange={(value) => handleConfigChange('variant', value)}>
                    <SelectTrigger className="h-11 text-sm border-gray-300">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {variants.map(variant => (
                        <SelectItem key={variant} value={variant}>
                          Layout {variant.toUpperCase()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Format & Size Settings */}
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">Format</Label>
                  <Select value={config.format} onValueChange={(value) => handleConfigChange('format', value)}>
                    <SelectTrigger className="h-11 text-sm border-gray-300">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {formats.map(format => (
                        <SelectItem key={format} value={format}>
                          .{format.toUpperCase()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">Size</Label>
                  <Select value={config.size} onValueChange={(value) => handleConfigChange('size', value)}>
                    <SelectTrigger className="h-11 text-sm border-gray-300">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {sizes.map(size => (
                        <SelectItem key={size} value={size}>
                          {size}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Format Info */}
              <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
                {config.format === 'svg' && '• Vector format - infinitely scalable'}
                {config.format === 'png' && '• Transparent background - perfect for overlays'}
                {config.format === 'jpg' && '• Includes background color from preview'}
              </div>
            </div>
          </div>

          {/* 2. Preview Section */}
          <div className="py-4">
            <h2 className="text-base font-medium text-gray-900 mb-4 flex items-center gap-2">
              <Image className="h-4 w-4 text-gray-600" />
              Logo Preview
            </h2>

            <div className={`${
              config.style === 'Dark'
                ? 'bg-gradient-to-br from-slate-800 to-slate-900'
                : 'bg-gradient-to-br from-white to-gray-50'
            } rounded-xl border border-gray-200 p-6 flex items-center justify-center h-64`}>
              {previewUrl ? (
                <img
                  src={previewUrl}
                  alt={`Ringerike Landskap Logo - ${config.style} Style, Variant ${config.variant.toUpperCase()}`}
                  className="w-full h-full object-contain"
                  onLoad={() => console.log('Logo loaded successfully:', previewUrl)}
                  onError={(e) => {
                    console.error('Logo preview failed to load:', previewUrl);
                    console.error('Error details:', e);
                  }}
                />
              ) : (
                <div className="text-center space-y-3">
                  <div className="w-12 h-12 bg-gray-200 rounded-lg mx-auto flex items-center justify-center">
                    <Palette className="h-6 w-6 text-gray-400" />
                  </div>
                  <p className="font-medium text-sm text-gray-500">Loading Preview...</p>
                </div>
              )}
            </div>
          </div>

          {/* 3. Download Section */}
          <div className="py-4">
            <h2 className="text-base font-medium text-gray-900 mb-4 flex items-center gap-2">
              <Download className="h-4 w-4 text-gray-600" />
              Download
            </h2>

            {/* Download Button - Primary Action First */}
            <Button
              onClick={handleDownload}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white h-12 text-base font-medium rounded-xl mb-4"
            >
              <Download className="h-5 w-5 mr-2" />
              Download Logo
            </Button>

            {/* File Info Card */}
            <div className="bg-gray-50 rounded-xl p-4">
              <div className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-700 block mb-1">Generated Filename:</Label>
                  <div className="bg-white p-3 rounded-lg border border-gray-200">
                    <code className="text-xs text-gray-800 break-all">{filename}</code>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600 block">Format</span>
                    <span className="text-gray-900">{config.format.toUpperCase()}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 block">Size</span>
                    <span className="text-gray-900">{config.format === 'svg' ? 'Vector' : getResolutionInfo(config.size)}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 block">Style</span>
                    <span className="text-gray-900">{config.style}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 block">Layout</span>
                    <span className="text-gray-900">{config.variant.toUpperCase()}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center py-6 px-4 text-gray-400 text-xs border-t border-gray-100">
          <p>© Ringerike Landskap - Professional Logo Assets</p>
        </div>

      </div>
    </div>
  );
};

export default LogoExporter;
