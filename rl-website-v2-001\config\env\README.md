# Environment Configuration

This directory contains environment-specific configuration files for the website runtime.

## File Structure

```

config/env/
├── .env.development     # Development environment configuration
├── .env.staging         # Staging environment configuration
├── .env.production      # Production environment configuration
├── .env.example         # Template for required environment variables
└── README.md            # This documentation

````

## Environment Variables

| Variable                | Description                                                      |
|------------------------|------------------------------------------------------------------|
| `VITE_BASE_URL`         | Base URL of the deployed frontend site                           |
| `VITE_API_URL`          | Base URL for API requests                                        |
| `VITE_DEBUG`            | Enable debug mode (`true` / `false`)                             |
| `VITE_ENABLE_ANALYTICS`| Enable analytics integration (`true` / `false`)                  |
| `VITE_ANALYTICS_ID`     | Analytics tracking ID (e.g., Google Analytics 4: `G-XXXXXXXXXX`) |
| `VITE_FEATURE_*`        | Feature toggles for conditional rendering/behavior               |

> All environment variables intended for use in frontend code **must** start with `VITE_` per [Vite's conventions](https://vitejs.dev/guide/env-and-mode.html).

## Validation

Run `npm run validate-env` to check that all environment files contain the required variables defined in `.env.example`.

The validation script uses `.env.example` as the source of truth for required variables, so any new environment variables should be added there first.

## Notes

* These files must not be committed to version control (they are git-ignored).
* Use `.env.example` to document all required variables.
* Avoid hardcoding content-related strings, use a config file instead (e.g. `site.config.ts`).